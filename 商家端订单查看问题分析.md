# 商家端订单查看问题分析

## 问题描述
用户反馈：商家端查看不了用户的订单

## 测试结果
经过详细测试，商家端订单查看功能**实际上是正常工作的**：

### ✅ 功能验证通过
1. **商家登录** - `POST /api/merchant/auth/login` ✅
2. **查看订单列表** - `GET /api/merchant/orders` ✅  
3. **查看订单详情** - `GET /api/merchant/orders/{order_id}` ✅

### 📊 测试数据
```json
// 商家登录成功
{
  "code": 200,
  "data": {
    "merchant_id": "M1382914",
    "token": "eyJhbGciOiJIUzI1NiIs...",
    "merchant_info": {...}
  }
}

// 订单列表查询成功
{
  "code": 200,
  "data": {
    "total": 1,
    "page": 1,
    "limit": 10,
    "orders": [
      {
        "order_id": "O0582130537",
        "user_id": "U3421901",
        "user_phone": "13900139999",
        "service_name": "宠物基础美容套餐",
        "pet_kind": "狗",
        "order_status": 2,
        "order_price": 88.0
      }
    ]
  }
}
```

## 可能的问题原因

### 1. 🔐 认证问题
**最常见原因**：Token认证失败
- **症状**：返回401错误"Token无效或已过期"
- **原因**：
  - Token格式错误（缺少"Bearer "前缀）
  - Token已过期（7天有效期）
  - 使用了错误的商家账号登录

**解决方案**：
```javascript
// 正确的请求头格式
headers: {
  "Authorization": "Bearer " + token,
  "Content-Type": "application/json"
}
```

### 2. 📊 数据关联问题
**症状**：能查看列表但订单为空
- **原因**：订单表中的`merchant_id`字段与当前商家ID不匹配
- **检查方法**：
```sql
-- 检查订单是否正确关联到商家
SELECT order_id, user_id, merchant_id, service_id 
FROM orders 
WHERE merchant_id = '当前商家ID';
```

### 3. 🔄 权限验证问题
**症状**：返回403错误"权限不足"
- **原因**：
  - 使用用户Token访问商家接口
  - Token中的`user_type`不是"merchant"

### 4. 🌐 接口路径问题
**常见错误**：
- ❌ `/api/merchant/login` (404错误)
- ✅ `/api/merchant/auth/login` (正确路径)

### 5. 📝 请求参数问题
**分页参数验证**：
```javascript
// 正确的分页参数
{
  page: 1,        // 必须 > 0
  limit: 10,      // 1-100之间
  order_status: 2 // 可选：0-已取消，1-已支付，2-已预约，3-已完成，4-未支付
}
```

## 🔧 调试步骤

### 步骤1：验证商家登录
```bash
curl -X POST http://localhost:5000/api/merchant/auth/login \
  -H "Content-Type: application/json" \
  -d '{"merchant_account": "petshop001", "merchant_password": "123456"}'
```

### 步骤2：使用Token查看订单
```bash
curl -X GET "http://localhost:5000/api/merchant/orders" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 步骤3：检查订单详情
```bash
curl -X GET "http://localhost:5000/api/merchant/orders/ORDER_ID" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 💡 解决建议

### 1. 前端检查清单
- [ ] 确认使用正确的API路径
- [ ] 验证Token格式和有效性
- [ ] 检查请求头设置
- [ ] 确认商家登录状态

### 2. 后端检查清单
- [ ] 验证订单数据中的merchant_id字段
- [ ] 检查Token验证逻辑
- [ ] 确认权限验证正确性
- [ ] 验证数据库关联关系

### 3. 测试用例
```javascript
// 测试商家查看订单功能
describe('商家订单查看', () => {
  test('应该能够查看订单列表', async () => {
    const token = await merchantLogin();
    const response = await request
      .get('/api/merchant/orders')
      .set('Authorization', `Bearer ${token}`);
    
    expect(response.status).toBe(200);
    expect(response.body.code).toBe(200);
    expect(response.body.data.orders).toBeDefined();
  });
});
```

## 📋 当前状态
- ✅ 商家认证功能正常
- ✅ 订单查看权限验证正常  
- ✅ 数据关联关系正确
- ✅ API接口响应正常

**结论**：商家端订单查看功能本身没有问题，可能是使用方式或环境配置导致的问题。
