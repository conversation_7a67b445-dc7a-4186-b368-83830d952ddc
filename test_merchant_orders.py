#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商家端订单查看功能测试脚本
用于验证商家端是否能正常查看用户订单
"""

import requests
import json
import sys

# API基础地址
BASE_URL = "http://localhost:5000/api"

def test_merchant_login():
    """测试商家登录"""
    print("🔐 测试商家登录...")
    
    url = f"{BASE_URL}/merchant/auth/login"
    data = {
        "merchant_account": "petshop001",
        "merchant_password": "123456"
    }
    
    try:
        response = requests.post(url, json=data)
        result = response.json()
        
        if result['code'] == 200:
            print("✅ 商家登录成功")
            print(f"   商家ID: {result['data']['merchant_id']}")
            return result['data']['token']
        else:
            print(f"❌ 商家登录失败: {result['message']}")
            return None
            
    except Exception as e:
        print(f"❌ 商家登录异常: {e}")
        return None

def test_get_orders(token):
    """测试获取订单列表"""
    print("\n📋 测试获取订单列表...")
    
    url = f"{BASE_URL}/merchant/orders"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        result = response.json()
        
        if result['code'] == 200:
            orders = result['data']['orders']
            total = result['data']['total']
            print(f"✅ 获取订单列表成功")
            print(f"   总订单数: {total}")
            
            if orders:
                print("   订单详情:")
                for order in orders:
                    print(f"     - 订单ID: {order['order_id']}")
                    print(f"       用户手机: {order.get('user_phone', 'N/A')}")
                    print(f"       服务名称: {order.get('service_name', 'N/A')}")
                    print(f"       订单状态: {order['order_status']}")
                    print(f"       订单金额: {order['order_price']}")
                return orders[0]['order_id'] if orders else None
            else:
                print("   暂无订单数据")
                return None
        else:
            print(f"❌ 获取订单列表失败: {result['message']}")
            return None
            
    except Exception as e:
        print(f"❌ 获取订单列表异常: {e}")
        return None

def test_get_order_detail(token, order_id):
    """测试获取订单详情"""
    print(f"\n🔍 测试获取订单详情 (ID: {order_id})...")
    
    url = f"{BASE_URL}/merchant/orders/{order_id}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        result = response.json()
        
        if result['code'] == 200:
            order = result['data']
            print("✅ 获取订单详情成功")
            print(f"   订单ID: {order['order_id']}")
            print(f"   用户信息: {order.get('user_info', {})}")
            print(f"   服务信息: {order.get('service_info', {})}")
            print(f"   宠物信息: {order.get('pet_info', {})}")
            print(f"   预约信息: {order.get('appointment_info', {})}")
            return True
        else:
            print(f"❌ 获取订单详情失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 获取订单详情异常: {e}")
        return False

def test_order_operations(token, order_id):
    """测试订单操作"""
    print(f"\n⚙️ 测试订单操作 (ID: {order_id})...")
    
    # 测试确认订单
    url = f"{BASE_URL}/merchant/orders/{order_id}/confirm"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.put(url, headers=headers)
        result = response.json()
        
        if result['code'] == 200:
            print("✅ 订单确认成功")
        else:
            print(f"ℹ️ 订单确认: {result['message']}")
            
    except Exception as e:
        print(f"❌ 订单确认异常: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试商家端订单查看功能")
    print("=" * 50)
    
    # 1. 测试商家登录
    token = test_merchant_login()
    if not token:
        print("\n❌ 商家登录失败，无法继续测试")
        sys.exit(1)
    
    # 2. 测试获取订单列表
    order_id = test_get_orders(token)
    if not order_id:
        print("\n⚠️ 没有订单数据，跳过详情测试")
        return
    
    # 3. 测试获取订单详情
    success = test_get_order_detail(token, order_id)
    if not success:
        print("\n❌ 订单详情获取失败")
        return
    
    # 4. 测试订单操作
    test_order_operations(token, order_id)
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！商家端订单查看功能正常")

if __name__ == "__main__":
    main()
